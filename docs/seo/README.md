# SEO Implementation Guide

> 🔍 **Comprehensive SEO setup for Pazogen website**

## Overview

This document outlines the complete SEO implementation for the Pazogen website, ensuring optimal search engine visibility and social media sharing.

## Architecture

```mermaid
graph TB
    A[App.tsx] --> B[HelmetProvider]
    B --> C[PageWrapper]
    C --> D[SEO Component]
    C --> E[Page Content]
    
    F[useSEO Hook] --> G[Route Detection]
    G --> H[SEO Config]
    H --> I[Dynamic Meta Tags]
    
    J[seo.ts Config] --> K[Route-Specific SEO]
    K --> L[Default SEO]
    
    style A fill:#e3f2fd
    style F fill:#f3e5f5
    style J fill:#e8f5e8
```

## Implementation Components

### 1. SEO Configuration (`src/config/seo.ts`)

Centralized configuration for all SEO data:

```typescript
export interface SEOConfig {
  title: string;
  description: string;
  keywords: string[];
  canonical?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: string;
  twitterCard?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  structuredData?: object;
}
```

### 2. SEO Component (`src/components/ui/SEO.tsx`)

Reusable component that renders all meta tags using React Helmet Async:

- Basic meta tags (title, description, keywords)
- Open Graph tags for social media
- Twitter Card tags
- Structured data (JSON-LD)
- Canonical URLs

### 3. useSEO Hook (`src/hooks/useSEO.ts`)

Custom hook that:
- Detects current route
- Returns appropriate SEO configuration
- Allows custom SEO overrides

### 4. PageWrapper Component (`src/components/ui/PageWrapper.tsx`)

Wrapper component that:
- Uses the useSEO hook
- Renders SEO component with route-specific data
- Wraps page content

## Route-Specific SEO

### Home Page (`/`)
- **Title**: "Pazogen – Water and Wastewater Engineering"
- **Description**: "An Original Equipment Manufacturer (OEM) for Water and Wastewater Engineering. Complete turnkey solutions for wastewater and water treatment systems."
- **Keywords**: water treatment, wastewater engineering, OEM water equipment, etc.
- **Structured Data**: Organization schema with contact information

### Projects Page (`/projects`)
- **Title**: "Our Projects – Pazogen Water Engineering"
- **Description**: "Explore our comprehensive water and wastewater engineering projects. From detailed design to installation and commissioning of treatment systems."
- **Keywords**: water treatment projects, wastewater engineering projects, etc.

### Gallery Page (`/gallery`)
- **Title**: "Project Gallery – Pazogen Water Treatment"
- **Description**: "View our extensive gallery of water and wastewater treatment projects, equipment installations, and engineering solutions."
- **Keywords**: water treatment gallery, wastewater project photos, etc.

### Quote Request Page (`/get-a-quote`)
- **Title**: "Get a Quote – Pazogen Water Engineering"
- **Description**: "Request a personalized quote for your water or wastewater treatment project. Professional engineering solutions tailored to your needs."
- **Keywords**: water treatment quote, wastewater engineering quote, etc.

### Quote Success Page (`/quote-success`)
- **Title**: "Quote Submitted – Pazogen Water Engineering"
- **Description**: "Thank you for your quote request. Our water engineering experts will contact you soon with a personalized solution."
- **Keywords**: quote submitted, water treatment consultation, etc.

## Technical Features

### 1. Dynamic Meta Tags
- Route-based title and description changes
- Automatic canonical URL generation
- Social media optimization

### 2. Structured Data
- Organization schema for business information
- Contact point information
- Address and service area data

### 3. Social Media Optimization
- Open Graph tags for Facebook, LinkedIn
- Twitter Card optimization
- Optimized image sharing

### 4. Search Engine Optimization
- Proper robots.txt configuration
- XML sitemap generation
- Canonical URL management
- Keyword optimization

## Files Added/Modified

### New Files
- `src/config/seo.ts` - SEO configuration
- `src/components/ui/SEO.tsx` - SEO component
- `src/hooks/useSEO.ts` - SEO hook
- `src/components/ui/PageWrapper.tsx` - Page wrapper
- `public/robots.txt` - Search engine directives
- `public/sitemap.xml` - Site structure for search engines

### Modified Files
- `src/App.tsx` - Added HelmetProvider and PageWrapper
- `index.html` - Enhanced base meta tags
- `package.json` - Added react-helmet-async dependency

## Usage

### Adding SEO to a New Route

1. Add route configuration to `src/config/seo.ts`:
```typescript
newRoute: {
  title: 'New Page – Pazogen',
  description: 'Description for new page',
  keywords: ['keyword1', 'keyword2'],
  canonical: `${baseUrl}/new-route`
}
```

2. Update the route mapping in `src/hooks/useSEO.ts`:
```typescript
case '/new-route':
  return 'newRoute';
```

3. Wrap the route component with PageWrapper in App.tsx:
```typescript
<Route
  path="/new-route"
  element={
    <PageWrapper>
      <NewComponent />
    </PageWrapper>
  }
/>
```

### Custom SEO for Specific Pages

```typescript
<PageWrapper customSEO={{
  title: 'Custom Title',
  description: 'Custom description'
}}>
  <YourComponent />
</PageWrapper>
```

## Best Practices

1. **Title Tags**: Keep under 60 characters
2. **Meta Descriptions**: Keep between 150-160 characters
3. **Keywords**: Use relevant, specific keywords
4. **Canonical URLs**: Always include canonical URLs
5. **Structured Data**: Include relevant schema markup
6. **Social Media**: Optimize for sharing with proper OG tags

## Testing

### SEO Testing Tools
- Google Search Console
- Facebook Sharing Debugger
- Twitter Card Validator
- Lighthouse SEO audit
- SEMrush Site Audit

### Manual Testing
1. View page source to verify meta tags
2. Test social media sharing
3. Validate structured data
4. Check canonical URLs
5. Verify robots.txt and sitemap.xml

## Performance Impact

- **Bundle Size**: Minimal impact (~15KB for react-helmet-async)
- **Runtime Performance**: No significant impact
- **SEO Benefits**: Significant improvement in search visibility
- **Social Sharing**: Enhanced appearance on social platforms
