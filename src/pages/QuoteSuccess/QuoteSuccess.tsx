import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import {
  CheckCircle2,
  ArrowLeft,

  Phone,
  Mail,
  MessageCircle,
  Clock,
  User,
  Building,
  Calendar,
  DollarSign,
  FileText,
  Zap
} from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import gsap from 'gsap';
import { formatZAR } from '../../utils/currency';

// Types for the data passed from QuoteRequest
interface FormData {
  name: string;
  email: string;
  phone: string;
  company?: string;
  service: string;
  projectType: string;
  timeline: string;
  budget: string;
  description: string;
  requirements: string[];
  files: File[];
  urgency: string;
  preferredContact: string;
  additionalNotes?: string;
}

interface LocationState {
  formData?: FormData;
  estimatedCost?: number;
}

const QuoteSuccess: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Get the form data from navigation state
  const state = location.state as LocationState;
  const formData = state?.formData;
  const estimatedCost = state?.estimatedCost;

  // Service options mapping (same as in QuoteRequest)
  const serviceOptions = {
    'water-treatment': 'Water Treatment Solutions',
    'consulting': 'Consulting Services',
    'maintenance': 'Maintenance & Support',
    'custom': 'Custom Solutions'
  };

  // Animate elements on mount
  useEffect(() => {
    if (containerRef.current) {
      gsap.fromTo(
        containerRef.current.children,
        { y: 30, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 0.8,
          stagger: 0.15,
          ease: 'power3.out',
        }
      );
    }
  }, []);

  // If no form data, redirect to quote request
  useEffect(() => {
    if (!formData) {
      navigate('/get-a-quote', { replace: true });
    }
  }, [formData, navigate]);

  if (!formData) {
    return null; // Will redirect
  }



  const handleContactUs = () => {
    navigate('/', { state: { scrollTo: 'contact' } });
  };

  const handleWhatsApp = () => {
    const message = `Hi! I just submitted a quote request for ${serviceOptions[formData.service as keyof typeof serviceOptions] || formData.service}. My reference is ${formData.name} - ${formData.email}. Looking forward to hearing from you!`;
    const whatsappUrl = `https://wa.me/27101096528?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-20 -right-20 sm:-top-40 sm:-right-40 w-40 h-40 sm:w-80 sm:h-80 bg-gradient-to-br from-green-400/20 to-emerald-400/20 rounded-full blur-2xl sm:blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-20 -left-20 sm:-bottom-40 sm:-left-40 w-40 h-40 sm:w-80 sm:h-80 bg-gradient-to-br from-teal-400/20 to-green-400/20 rounded-full blur-2xl sm:blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 sm:w-96 sm:h-96 bg-gradient-to-br from-emerald-400/10 to-teal-400/10 rounded-full blur-2xl sm:blur-3xl animate-spin-slow"></div>
      </div>

      {/* Main content */}
      <div className="min-h-screen flex flex-col relative z-10">
        {/* Header */}
        <motion.div
          className="px-4 pt-4 pb-2 sm:px-6 sm:pt-8 sm:pb-4 lg:px-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Back button */}
          <motion.button
            onClick={() => navigate('/')}
            className="mb-4 sm:mb-6 flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 transition-colors group p-2 -ml-2 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-950/20"
            whileHover={{ x: -4 }}
            whileTap={{ scale: 0.95 }}
          >
            <ArrowLeft className="w-5 h-5 mr-2 group-hover:animate-pulse" />
            <span className="font-medium">Back to Home</span>
          </motion.button>
        </motion.div>

        {/* Success content */}
        <div className="flex-1 px-4 sm:px-6 lg:px-8 pb-4 sm:pb-8">
          <div ref={containerRef} className="max-w-4xl mx-auto space-y-6 sm:space-y-8">
            
            {/* Success header */}
            <motion.div
              className="text-center bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl rounded-2xl sm:rounded-3xl shadow-xl sm:shadow-2xl border border-white/20 dark:border-gray-700/30 p-6 sm:p-8 lg:p-12"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6 }}
            >
              <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                <CheckCircle2 className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
              </div>
              
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-3 sm:mb-4">
                Quote Request Submitted Successfully!
              </h1>
              
              <p className="text-base sm:text-lg text-gray-600 dark:text-gray-400 mb-6 sm:mb-8 max-w-2xl mx-auto leading-relaxed">
                Thank you for choosing Pazogen! We've received your quote request and our team will review your requirements carefully.
              </p>

              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 text-center">
                <div className="flex flex-col items-center p-4 bg-green-50 dark:bg-green-950/20 rounded-xl border border-green-200 dark:border-green-800">
                  <Clock className="w-6 h-6 text-green-600 dark:text-green-400 mb-2" />
                  <span className="text-sm font-medium text-green-800 dark:text-green-200">Response Time</span>
                  <span className="text-xs text-green-600 dark:text-green-400">Within 24 hours</span>
                </div>
                
                <div className="flex flex-col items-center p-4 bg-blue-50 dark:bg-blue-950/20 rounded-xl border border-blue-200 dark:border-blue-800">
                  <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400 mb-2" />
                  <span className="text-sm font-medium text-blue-800 dark:text-blue-200">Detailed Proposal</span>
                  <span className="text-xs text-blue-600 dark:text-blue-400">Custom solution</span>
                </div>
                
                <div className="flex flex-col items-center p-4 bg-purple-50 dark:bg-purple-950/20 rounded-xl border border-purple-200 dark:border-purple-800">
                  <Zap className="w-6 h-6 text-purple-600 dark:text-purple-400 mb-2" />
                  <span className="text-sm font-medium text-purple-800 dark:text-purple-200">Expert Consultation</span>
                  <span className="text-xs text-purple-600 dark:text-purple-400">Free of charge</span>
                </div>
              </div>
            </motion.div>

            {/* Quote summary */}
            <motion.div
              className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl rounded-2xl sm:rounded-3xl shadow-xl sm:shadow-2xl border border-white/20 dark:border-gray-700/30 p-6 sm:p-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Quote Summary
              </h2>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Personal Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                    <User className="w-5 h-5 mr-2 text-primary-600 dark:text-primary-400" />
                    Contact Information
                  </h3>
                  
                  <div className="space-y-3 pl-7">
                    <div>
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Name:</span>
                      <p className="text-gray-900 dark:text-white">{formData.name}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Email:</span>
                      <p className="text-gray-900 dark:text-white">{formData.email}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Phone:</span>
                      <p className="text-gray-900 dark:text-white">{formData.phone}</p>
                    </div>
                    {formData.company && (
                      <div>
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Company:</span>
                        <p className="text-gray-900 dark:text-white">{formData.company}</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Project Details */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                    <Building className="w-5 h-5 mr-2 text-primary-600 dark:text-primary-400" />
                    Project Details
                  </h3>
                  
                  <div className="space-y-3 pl-7">
                    <div>
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Service:</span>
                      <p className="text-gray-900 dark:text-white">
                        {serviceOptions[formData.service as keyof typeof serviceOptions] || formData.service}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Project Type:</span>
                      <p className="text-gray-900 dark:text-white">{formData.projectType}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Timeline:</span>
                      <p className="text-gray-900 dark:text-white">{formData.timeline}</p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Budget Range:</span>
                      <p className="text-gray-900 dark:text-white">{formData.budget}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Project Description */}
              {formData.description && (
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
                    Project Description
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    {formData.description}
                  </p>
                </div>
              )}

              {/* Requirements */}
              {formData.requirements && formData.requirements.length > 0 && (
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
                    Requirements
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {formData.requirements.map((requirement, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-primary-100 dark:bg-primary-950/30 text-primary-800 dark:text-primary-200 text-sm rounded-full border border-primary-200 dark:border-primary-800"
                      >
                        {requirement}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>

            {/* Action buttons */}
            <motion.div
              className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl rounded-2xl sm:rounded-3xl shadow-xl sm:shadow-2xl border border-white/20 dark:border-gray-700/30 p-6 sm:p-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
                What's Next?
              </h2>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                {/* Contact Us */}
                <motion.button
                  onClick={handleContactUs}
                  className="flex flex-col items-center p-6 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-200 shadow-lg hover:shadow-xl"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Phone className="w-8 h-8 mb-3" />
                  <span className="font-semibold text-lg mb-1">Contact Us</span>
                  <span className="text-sm opacity-90 text-center">Speak with our experts</span>
                </motion.button>

                {/* WhatsApp */}
                <motion.button
                  onClick={handleWhatsApp}
                  className="flex flex-col items-center p-6 bg-gradient-to-r from-green-600 to-green-500 text-white rounded-xl hover:from-green-700 hover:to-green-600 transition-all duration-200 shadow-lg hover:shadow-xl"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <MessageCircle className="w-8 h-8 mb-3" />
                  <span className="font-semibold text-lg mb-1">WhatsApp</span>
                  <span className="text-sm opacity-90 text-center">Quick chat with our team</span>
                </motion.button>
              </div>
            </motion.div>

            {/* Contact information */}
            <motion.div
              className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl rounded-2xl sm:rounded-3xl shadow-xl sm:shadow-2xl border border-white/20 dark:border-gray-700/30 p-6 sm:p-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-6 text-center">
                Need Immediate Assistance?
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Phone */}
                <div className="flex items-center p-4 bg-blue-50 dark:bg-blue-950/20 rounded-xl border border-blue-200 dark:border-blue-800">
                  <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                    <Phone className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-blue-800 dark:text-blue-200">Call Us</h3>
                    <p className="text-blue-600 dark:text-blue-400">+27 (0) 10 109 6528</p>
                    <p className="text-xs text-blue-500 dark:text-blue-500">Mon-Fri: 8AM-5PM</p>
                  </div>
                </div>

                {/* Email */}
                <div className="flex items-center p-4 bg-purple-50 dark:bg-purple-950/20 rounded-xl border border-purple-200 dark:border-purple-800">
                  <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mr-4 flex-shrink-0">
                    <Mail className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-purple-800 dark:text-purple-200">Email Us</h3>
                    <p className="text-purple-600 dark:text-purple-400"><EMAIL></p>
                    <p className="text-xs text-purple-500 dark:text-purple-500">24/7 Support</p>
                  </div>
                </div>
              </div>

              {/* Additional info */}
              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 text-center">
                <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                  Our team of water treatment experts will review your requirements and provide a comprehensive proposal
                  tailored to your specific needs. We'll contact you within 24 hours to discuss your project in detail.
                </p>
              </div>
            </motion.div>

            {/* Reference number */}
            <motion.div
              className="text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              <p className="text-sm text-gray-500 dark:text-gray-500">
                Reference: {formData.name.replace(/\s+/g, '').toUpperCase()}-{Date.now().toString().slice(-6)}
              </p>
            </motion.div>

          </div>
        </div>
      </div>
    </div>
  );
};

export default QuoteSuccess;
