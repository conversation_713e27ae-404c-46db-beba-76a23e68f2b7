import React, { useEffect, useRef, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowLeft,
  ArrowRight,
  // Calculator, // COMMENTED OUT - Calculator related
  User,
  Mail,
  Phone,
  FileText,
  Zap,
  Droplets,
  Settings,
  Shield,
  AlertCircle,
  CheckCircle2,
  // TrendingUp, // COMMENTED OUT - Unused
  RefreshCw,
  X,
  // Send // COMMENTED OUT - Unused
} from 'lucide-react';
import gsap from 'gsap';
import { useNavigate /* , useLocation */ } from 'react-router-dom'; // COMMENTED OUT - useLocation for calculator data
import emailjs from '@emailjs/browser';
// COMMENTED OUT - Calculator related imports
// import AdvancedCalculator, { CalculatorParams, CostBreakdown } from '../../components/ui/AdvancedCalculator';
import { formatBudgetRange, /* getBudgetR<PERSON><PERSON><PERSON><PERSON>, CURRENCY_CONFIG, */ formatZAR } from '../../utils/currency'; // COMMENTED OUT - Calculator related imports

// Types for form data
interface FormData {
  // Step 1: Personal Info
  name: string;
  email: string;
  phone: string;
  company?: string;

  // Step 2: Project Details
  service: string;
  projectType: string;
  timeline: string;
  budget: string;

  // Step 3: Requirements
  description: string;
  requirements: string[];
  files: File[];

  // Step 4: Additional Info
  urgency: string;
  preferredContact: string;
  additionalNotes?: string;
}

interface FormErrors {
  [key: string]: string;
}

type MessageType = 'success' | 'error' | 'info';

interface StatusMessage {
  type: MessageType;
  title: string;
  message: string;
  showRetry?: boolean;
}

interface ServiceOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  basePrice: number;
  features: string[];
}

const QuoteRequest: React.FC = () => {
  const formRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  // const location = useLocation(); // COMMENTED OUT - Calculator related

  // COMMENTED OUT - Calculator related data
  // const calculatorData = location.state?.calculatorData as { params: CalculatorParams; cost: CostBreakdown } | undefined;

  // Form state
  const [currentStep, setCurrentStep] = useState(1); // COMMENTED OUT - Skip to step 2 if we have calculator data
  // const [showCalculator, setShowCalculator] = useState(false); // COMMENTED OUT - Calculator related
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    service: '', // COMMENTED OUT - calculatorData?.params.serviceType || '',
    projectType: '',
    timeline: '', // COMMENTED OUT - calculatorData?.params.urgency || '',
    budget: '', // COMMENTED OUT - calculatorData ? getBudgetRangeKey(calculatorData.cost.total) : '',
    description: '', // COMMENTED OUT - calculatorData ? `Project requirements based on calculator: Flow rate ${calculatorData.params.flowRate} L/min, ${calculatorData.params.treatmentLevel} treatment level` : '',
    requirements: [], // COMMENTED OUT - calculatorData?.params.additionalFeatures || [],
    files: [],
    urgency: '', // COMMENTED OUT - calculatorData?.params.urgency || '',
    preferredContact: '',
    additionalNotes: ''
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [statusMessage, setStatusMessage] = useState<StatusMessage | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  // COMMENTED OUT - Calculator related state
  // const [estimatedCost, setEstimatedCost] = useState(calculatorData?.cost.total || 0);
  // const [calculatorParams, setCalculatorParams] = useState<CalculatorParams | null>(calculatorData?.params || null);

  // Service options
  const serviceOptions: ServiceOption[] = [
    {
      id: 'water-treatment',
      name: 'Water Treatment Solutions',
      description: 'Advanced filtration and purification systems',
      icon: <Droplets className="w-8 h-8" />,
      basePrice: 285000, // R285,000 (increased by 200% from R95,000)
      features: ['Custom filtration', 'Quality monitoring', '24/7 support']
    },
    {
      id: 'consulting',
      name: 'Consulting Services',
      description: 'Expert guidance and strategic planning',
      icon: <Settings className="w-8 h-8" />,
      basePrice: 142500, // R142,500 (increased by 200% from R47,500)
      features: ['Strategic planning', 'Process optimization', 'Compliance guidance']
    },
    {
      id: 'maintenance',
      name: 'Maintenance & Support',
      description: 'Ongoing system maintenance and support',
      icon: <Shield className="w-8 h-8" />,
      basePrice: 85500, // R85,500 (increased by 200% from R28,500)
      features: ['Regular maintenance', 'Emergency support', 'Performance monitoring']
    },
    {
      id: 'custom',
      name: 'Custom Solutions',
      description: 'Tailored solutions for unique requirements',
      icon: <Zap className="w-8 h-8" />,
      basePrice: 427500, // R427,500 (increased by 200% from R142,500)
      features: ['Custom development', 'Integration services', 'Specialized equipment']
    }
  ];

  const totalSteps = 4;

  // Status message helpers
  const clearStatusMessage = () => {
    setStatusMessage(null);
  };

  const showSuccessMessage = () => {
    setStatusMessage({
      type: 'success',
      title: 'Quote Request Submitted Successfully!',
      message: 'Thank you for your quote request. Our team will review your requirements and get back to you within 24 hours with a detailed proposal.',
    });
  };

  const showErrorMessage = (error: any) => {
    let title = 'Quote Request Failed';
    let message = 'We\'re sorry, but there was an issue submitting your quote request. ';
    let showRetry = true;

    // Determine error type and customize message
    if (error?.status === 400) {
      title = 'Invalid Information';
      message = 'Please check your information and try again. ';
      showRetry = true;
    } else if (error?.status === 429) {
      title = 'Too Many Requests';
      message = 'You\'ve submitted too many requests recently. Please wait a few minutes before trying again. ';
      showRetry = false;
    } else if (error?.status >= 500) {
      title = 'Server Error';
      message = 'Our servers are experiencing issues. ';
      showRetry = true;
    } else if (error?.name === 'NetworkError' || !navigator.onLine) {
      title = 'Connection Error';
      message = 'Please check your internet connection and try again. ';
      showRetry = true;
    }

    message += 'You can also contact us <NAME_EMAIL> or +27 (0) 10 109 6528 for immediate assistance.';

    setStatusMessage({
      type: 'error',
      title,
      message,
      showRetry,
    });
  };

  // Form validation
  const validateStep = useCallback((step: number): boolean => {
    const newErrors: FormErrors = {};

    switch (step) {
      case 1:
        if (!formData.name.trim()) newErrors.name = 'Name is required';
        if (!formData.email.trim()) {
          newErrors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
          newErrors.email = 'Please enter a valid email';
        }
        if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
        break;

      case 2:
        if (!formData.service) newErrors.service = 'Please select a service';
        if (!formData.projectType) newErrors.projectType = 'Please specify project type';
        if (!formData.timeline) newErrors.timeline = 'Please select a timeline';
        if (!formData.budget) newErrors.budget = 'Please select a budget range';
        break;

      case 3:
        if (!formData.description.trim()) newErrors.description = 'Project description is required';
        if (formData.requirements.length === 0) newErrors.requirements = 'Please select at least one requirement';
        break;

      case 4:
        if (!formData.urgency) newErrors.urgency = 'Please specify urgency level';
        if (!formData.preferredContact) newErrors.preferredContact = 'Please select preferred contact method';
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  // COMMENTED OUT - Calculate estimated cost
  // const calculateEstimatedCost = useCallback(() => {
  //   const selectedService = serviceOptions.find(s => s.id === formData.service);
  //   if (!selectedService) return 0;

  //   let cost = selectedService.basePrice;

  //   // Timeline multiplier
  //   const timelineMultipliers: { [key: string]: number } = {
  //     'urgent': 1.5,
  //     '1-month': 1.2,
  //     '3-months': 1.0,
  //     '6-months': 0.9,
  //     'flexible': 0.8
  //   };

  //   // Budget adjustments (Updated for ZAR ranges)
  //   const budgetMultipliers = CURRENCY_CONFIG.BUDGET_MULTIPLIERS;

  //   cost *= timelineMultipliers[formData.timeline] || 1;
  //   cost *= budgetMultipliers[formData.budget as keyof typeof budgetMultipliers] || 1;

  //   // Requirements multiplier
  //   cost *= (1 + formData.requirements.length * 0.1);

  //   return Math.round(cost);
  // }, [formData, serviceOptions]);

  // COMMENTED OUT - Update estimated cost when relevant fields change
  // useEffect(() => {
  //   setEstimatedCost(calculateEstimatedCost());
  // }, [formData.service, formData.timeline, formData.budget, formData.requirements, calculateEstimatedCost]);

  // Handle form field changes
  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    // Clear status message when user starts typing
    if (statusMessage) {
      clearStatusMessage();
    }
  };

  // Handle step navigation
  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  // Create comprehensive email template data
  const createEmailTemplateData = () => {
    const selectedService = serviceOptions.find(s => s.id === formData.service);

    return {
      // Personal Information
      from_name: formData.name,
      reply_to: formData.email,
      phone_number: formData.phone,
      company: formData.company || 'Not specified',

      // Service Details
      service_type: selectedService?.name || formData.service,
      project_type: formData.projectType,
      timeline: formData.timeline,
      budget_range: formData.budget,
      // COMMENTED OUT - Calculator related estimated cost
      // estimated_cost: formatZAR(estimatedCost),

      // Project Requirements
      project_description: formData.description,
      requirements_list: formData.requirements.join(', '),
      urgency_level: formData.urgency,
      preferred_contact: formData.preferredContact,
      additional_notes: formData.additionalNotes || 'None',

      // COMMENTED OUT - Calculator Data (if available)
      // calculator_data: calculatorData ? `
      //   Flow Rate: ${calculatorData.params.flowRate.toLocaleString()} L/min
      //   Treatment Level: ${calculatorData.params.treatmentLevel}
      //   Service Type: ${calculatorData.params.serviceType}
      //   Urgency: ${calculatorData.params.urgency}
      //   Additional Features: ${calculatorData.params.additionalFeatures.join(', ')}
      //   Calculated Cost: ${formatZAR(calculatorData.cost.total)}
      // ` : 'No calculator data provided',

      // Submission Details
      submission_date: new Date().toLocaleString(),
      form_source: 'Quote Request Form'
    };
  };

  // Submit form with EmailJS
  const submitQuoteRequest = async () => {
    setIsSubmitting(true);
    clearStatusMessage();

    try {
      const templateData = createEmailTemplateData();

      const result = await emailjs.send(
        'service_rm1uva9', // Same service ID as Contact form
        'template_quote_request', // New template for quote requests
        templateData,
        'dd5oCAyB_onRRV53J' // Same public key
      );

      if (result.status === 200) {
        showSuccessMessage();
        setRetryCount(0);

        // Redirect to success page after showing message
        setTimeout(() => {
          navigate('/quote-success', {
            state: {
              formData,
              // COMMENTED OUT - Calculator related data
              // estimatedCost,
              // calculatorData
            }
          });
        }, 3000);
      }
    } catch (error) {
      console.error('Failed to send quote request:', error);
      setRetryCount(prev => prev + 1);
      showErrorMessage(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateStep(currentStep)) return;

    await submitQuoteRequest();
  };

  // Handle retry
  const handleRetry = async () => {
    if (validateStep(currentStep)) {
      await submitQuoteRequest();
    }
  };

  // Animate form entrance
  useEffect(() => {
    if (formRef.current) {
      gsap.fromTo(
        formRef.current.children,
        { y: 20, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: 'power3.out',
        }
      );
    }
  }, [currentStep]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-cyan-50 to-teal-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden">
      {/* Animated background elements - optimized for mobile */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-20 -right-20 sm:-top-40 sm:-right-40 w-40 h-40 sm:w-80 sm:h-80 bg-gradient-to-br from-blue-400/20 to-cyan-400/20 rounded-full blur-2xl sm:blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-20 -left-20 sm:-bottom-40 sm:-left-40 w-40 h-40 sm:w-80 sm:h-80 bg-gradient-to-br from-teal-400/20 to-blue-400/20 rounded-full blur-2xl sm:blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 sm:w-96 sm:h-96 bg-gradient-to-br from-cyan-400/10 to-teal-400/10 rounded-full blur-2xl sm:blur-3xl animate-spin-slow"></div>
      </div>

      {/* Mobile-optimized container */}
      <div className="min-h-screen flex flex-col relative z-10">
        {/* Mobile-first header */}
        <motion.div
          className="px-4 pt-4 pb-2 sm:px-6 sm:pt-8 sm:pb-4 lg:px-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Back button - mobile optimized */}
          <motion.button
            onClick={() => navigate(-1)}
            className="mb-4 sm:mb-6 flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 transition-colors group p-2 -ml-2 rounded-lg hover:bg-primary-50 dark:hover:bg-primary-950/20"
            whileHover={{ x: -4 }}
            whileTap={{ scale: 0.95 }}
          >
            <ArrowLeft className="w-5 h-5 mr-2 group-hover:animate-pulse" />
            <span className="font-medium">Back</span>
          </motion.button>

          {/* Mobile-optimized header */}
          <div className="mb-4 sm:mb-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
              <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2 sm:mb-0 leading-tight">
                Get Your Custom Quote
              </h1>
              <div className="flex items-center">
                <div className="text-sm font-medium text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded-full">
                  Step {currentStep} of {totalSteps}
                </div>
              </div>
            </div>

            {/* Mobile-optimized progress bar */}
            <div className="relative">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 sm:h-2 overflow-hidden">
                <motion.div
                  className="h-full bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full relative"
                  initial={{ width: 0 }}
                  animate={{ width: `${(currentStep / totalSteps) * 100}%` }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                >
                  {/* Progress indicator dot */}
                  <div className="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 w-4 h-4 sm:w-3 sm:h-3 bg-primary-600 rounded-full border-2 border-white dark:border-gray-900 shadow-lg"></div>
                </motion.div>
              </div>

              {/* Step indicators for mobile */}
              <div className="flex justify-between mt-2 sm:hidden">
                {Array.from({ length: totalSteps }, (_, i) => (
                  <div
                    key={i}
                    className={`text-xs font-medium px-2 py-1 rounded ${
                      i + 1 <= currentStep
                        ? 'text-primary-600 dark:text-primary-400'
                        : 'text-gray-400 dark:text-gray-600'
                    }`}
                  >
                    {i + 1 === 1 && 'Info'}
                    {i + 1 === 2 && 'Service'}
                    {i + 1 === 3 && 'Details'}
                    {i + 1 === 4 && 'Review'}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Mobile-optimized main form container */}
        <div className="flex-1 px-4 sm:px-6 lg:px-8 pb-4 sm:pb-8">
          <motion.div
            ref={formRef}
            className="max-w-4xl mx-auto bg-white/90 dark:bg-gray-800/90 backdrop-blur-xl rounded-2xl sm:rounded-3xl shadow-xl sm:shadow-2xl border border-white/20 dark:border-gray-700/30 overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <form onSubmit={handleSubmit} className="relative h-full flex flex-col">
              {/* Step content - mobile optimized */}
              <div className="flex-1 p-4 sm:p-6 lg:p-8">
                <AnimatePresence mode="wait">
                  {currentStep === 1 && (
                    <motion.div
                      key="step1"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                      className="h-full"
                    >
                      {/* Step 1: Personal Information - Mobile First */}
                      <div className="mb-6 sm:mb-8">
                        {/* Mobile-optimized step header */}
                        <div className="flex items-start sm:items-center mb-6 sm:mb-8">
                          <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
                            <User className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                          </div>
                          <div className="min-w-0 flex-1">
                            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white leading-tight">Personal Information</h2>
                            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mt-1">Tell us about yourself</p>
                          </div>
                        </div>

                        {/* Mobile-first form grid */}
                        <div className="space-y-4 sm:space-y-6">
                          {/* Full width on mobile, grid on larger screens */}
                          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                            {/* Name field - Mobile optimized */}
                            <div className="relative">
                              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                Full Name *
                              </label>
                              <div className="relative">
                                <input
                                  type="text"
                                  value={formData.name}
                                  onChange={(e) => handleInputChange('name', e.target.value)}
                                  className={`w-full px-4 py-4 sm:py-3 pl-12 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm ${
                                    errors.name
                                      ? 'border-error-500 focus:border-error-600'
                                      : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                                  } focus:ring-2 focus:ring-primary-500/20 focus:outline-none`}
                                  placeholder="Enter your full name"
                                />
                                <User className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                              </div>
                              {errors.name && (
                                <motion.p
                                  initial={{ opacity: 0, y: -10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                                >
                                  <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                                  {errors.name}
                                </motion.p>
                              )}
                            </div>

                            {/* Email field - Mobile optimized */}
                            <div className="relative">
                              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                Email Address *
                              </label>
                              <div className="relative">
                                <input
                                  type="email"
                                  value={formData.email}
                                  onChange={(e) => handleInputChange('email', e.target.value)}
                                  className={`w-full px-4 py-4 sm:py-3 pl-12 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm ${
                                    errors.email
                                      ? 'border-error-500 focus:border-error-600'
                                      : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                                  } focus:ring-2 focus:ring-primary-500/20 focus:outline-none`}
                                  placeholder="Enter your email address"
                                />
                                <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                              </div>
                              {errors.email && (
                                <motion.p
                                  initial={{ opacity: 0, y: -10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                                >
                                  <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                                  {errors.email}
                                </motion.p>
                              )}
                            </div>

                            {/* Phone field - Mobile optimized */}
                            <div className="relative">
                              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                Phone Number *
                              </label>
                              <div className="relative">
                                <input
                                  type="tel"
                                  value={formData.phone}
                                  onChange={(e) => handleInputChange('phone', e.target.value)}
                                  className={`w-full px-4 py-4 sm:py-3 pl-12 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm ${
                                    errors.phone
                                      ? 'border-error-500 focus:border-error-600'
                                      : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                                  } focus:ring-2 focus:ring-primary-500/20 focus:outline-none`}
                                  placeholder="+****************"
                                />
                                <Phone className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                              </div>
                              {errors.phone && (
                                <motion.p
                                  initial={{ opacity: 0, y: -10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                                >
                                  <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                                  {errors.phone}
                                </motion.p>
                              )}
                            </div>

                            {/* Company field - Mobile optimized */}
                            <div className="relative">
                              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                Company (Optional)
                              </label>
                              <input
                                type="text"
                                value={formData.company}
                                onChange={(e) => handleInputChange('company', e.target.value)}
                                className="w-full px-4 py-4 sm:py-3 rounded-xl border-2 border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm focus:outline-none"
                                placeholder="Your company name"
                              />
                            </div>
                          </div>

                          {/* Mobile-specific help text */}
                          <div className="mt-6 p-4 bg-primary-50 dark:bg-primary-950/20 rounded-xl border border-primary-200 dark:border-primary-800 sm:hidden">
                            <div className="flex items-start">
                              <div className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2 flex-shrink-0 mt-0.5">
                                <svg fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <div>
                                <p className="text-sm font-medium text-primary-800 dark:text-primary-200">Quick Tip</p>
                                <p className="text-xs text-primary-700 dark:text-primary-300 mt-1">
                                  We'll use this information to personalize your quote and contact you with updates.
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}

                  {currentStep === 2 && (
                    <motion.div
                      key="step2"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                      className="h-full"
                    >
                    {/* Step 2: Service Selection - Mobile First */}
                    <div className="mb-6 sm:mb-8">
                      {/* Mobile-optimized step header */}
                      <div className="flex items-start sm:items-center mb-6 sm:mb-8">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-r from-secondary-500 to-accent-500 rounded-full flex items-center justify-center mr-3 sm:mr-4 flex-shrink-0">
                          <Settings className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white leading-tight">Service Selection</h2>
                          <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mt-1">Choose the service that best fits your needs</p>
                        </div>
                      </div>

                      {/* COMMENTED OUT - Calculator Integration Notice */}
                      {/* {calculatorData && (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-200 dark:border-green-800"
                        >
                          <div className="flex items-start">
                            <div className="w-5 h-5 text-green-600 dark:text-green-400 mr-3 flex-shrink-0 mt-0.5">
                              <Calculator />
                            </div>
                            <div className="flex-1">
                              <h4 className="text-sm font-semibold text-green-800 dark:text-green-200 mb-1">
                                Calculator Data Imported
                              </h4>
                              <p className="text-xs text-green-700 dark:text-green-300 mb-2">
                                We've pre-filled your form with data from the calculator. You can modify any details below.
                              </p>
                              <div className="text-xs text-green-600 dark:text-green-400 space-y-1">
                                <div>Service: {serviceOptions.find(s => s.id === calculatorData.params.serviceType)?.name}</div>
                                <div>Flow Rate: {calculatorData.params.flowRate.toLocaleString()} L/min</div>
                                <div>Treatment Level: {calculatorData.params.treatmentLevel}</div>
                                <div>Estimated Cost: {formatZAR(calculatorData.cost.total)}</div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      )} */}

                      {/* COMMENTED OUT - Quick Calculator Access */}
                      {/* {!calculatorData && (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-start">
                              <div className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-3 flex-shrink-0 mt-0.5">
                                <Calculator />
                              </div>
                              <div>
                                <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-1">
                                  Need Help Estimating Costs?
                                </h4>
                                <p className="text-xs text-blue-700 dark:text-blue-300">
                                  Use our advanced calculator to get detailed cost estimates before submitting your quote.
                                </p>
                              </div>
                            </div>
                            <motion.button
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                              onClick={() => navigate('/calculator')}
                              className="ml-3 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-xs font-medium rounded-lg transition-colors flex-shrink-0"
                            >
                              Use Calculator
                            </motion.button>
                          </div>
                        </motion.div>
                      )} */}

                      {/* Mobile-optimized service options */}
                      <div className="space-y-3 sm:space-y-4 lg:grid lg:grid-cols-2 lg:gap-4 lg:space-y-0 mb-6 sm:mb-8">
                        {serviceOptions.map((service) => (
                          <motion.div
                            key={service.id}
                            className={`relative p-4 sm:p-6 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                              formData.service === service.id
                                ? 'border-primary-500 bg-primary-50 dark:bg-primary-950/20 shadow-lg'
                                : 'border-gray-300 dark:border-gray-600 hover:border-primary-300 bg-white/70 dark:bg-gray-700/70 hover:shadow-md'
                            }`}
                            onClick={() => handleInputChange('service', service.id)}
                            whileHover={{ scale: 1.01 }}
                            whileTap={{ scale: 0.99 }}
                          >
                            <div className="flex items-start space-x-3 sm:space-x-4">
                              <div className={`p-2 sm:p-3 rounded-lg flex-shrink-0 ${
                                formData.service === service.id
                                  ? 'bg-primary-500 text-white'
                                  : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                              }`}>
                                {service.icon}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-start justify-between">
                                  <div className="flex-1 min-w-0">
                                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-1 sm:mb-2">
                                      {service.name}
                                    </h3>
                                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 sm:mb-3 leading-relaxed">
                                      {service.description}
                                    </p>
                                    <div className="text-sm text-gray-500 dark:text-gray-500">
                                      Starting from <span className="font-semibold text-primary-600 dark:text-primary-400 text-base">
                                        {formatZAR(service.basePrice)}
                                      </span>
                                    </div>
                                  </div>
                                  {formData.service === service.id && (
                                    <motion.div
                                      initial={{ scale: 0 }}
                                      animate={{ scale: 1 }}
                                      className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center ml-2 flex-shrink-0"
                                    >
                                      <CheckCircle2 className="w-4 h-4 text-white" />
                                    </motion.div>
                                  )}
                                </div>

                                {/* Mobile-specific features list */}
                                {formData.service === service.id && (
                                  <motion.div
                                    initial={{ opacity: 0, height: 0 }}
                                    animate={{ opacity: 1, height: 'auto' }}
                                    className="mt-3 pt-3 border-t border-primary-200 dark:border-primary-800 sm:hidden"
                                  >
                                    <p className="text-xs font-medium text-primary-700 dark:text-primary-300 mb-2">Includes:</p>
                                    <ul className="space-y-1">
                                      {service.features.map((feature, index) => (
                                        <li key={index} className="text-xs text-primary-600 dark:text-primary-400 flex items-center">
                                          <div className="w-1 h-1 bg-primary-500 rounded-full mr-2 flex-shrink-0"></div>
                                          {feature}
                                        </li>
                                      ))}
                                    </ul>
                                  </motion.div>
                                )}
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>

                      {errors.service && (
                        <motion.p
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mb-4 sm:mb-6 text-sm text-error-600 dark:text-error-400 flex items-center bg-error-50 dark:bg-error-950/20 p-3 rounded-lg border border-error-200 dark:border-error-800"
                        >
                          <AlertCircle className="w-4 h-4 mr-2 flex-shrink-0" />
                          {errors.service}
                        </motion.p>
                      )}

                      {/* Mobile-optimized additional project details */}
                      <div className="space-y-4 sm:space-y-6 lg:grid lg:grid-cols-2 lg:gap-6 lg:space-y-0">
                        {/* Project Type */}
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                            Project Type *
                          </label>
                          <select
                            value={formData.projectType}
                            onChange={(e) => handleInputChange('projectType', e.target.value)}
                            className={`w-full px-4 py-4 sm:py-3 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm ${
                              errors.projectType
                                ? 'border-error-500 focus:border-error-600'
                                : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                            } focus:ring-2 focus:ring-primary-500/20 focus:outline-none appearance-none`}
                          >
                            <option value="">Select project type</option>
                            <option value="new-installation">New Installation</option>
                            <option value="upgrade">System Upgrade</option>
                            <option value="maintenance">Maintenance Contract</option>
                            <option value="consultation">Consultation Only</option>
                            <option value="emergency">Emergency Service</option>
                          </select>
                          {errors.projectType && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                              {errors.projectType}
                            </motion.p>
                          )}
                        </div>

                        {/* Timeline */}
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                            Timeline *
                          </label>
                          <select
                            value={formData.timeline}
                            onChange={(e) => handleInputChange('timeline', e.target.value)}
                            className={`w-full px-4 py-4 sm:py-3 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm ${
                              errors.timeline
                                ? 'border-error-500 focus:border-error-600'
                                : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                            } focus:ring-2 focus:ring-primary-500/20 focus:outline-none appearance-none`}
                          >
                            <option value="">Select timeline</option>
                            <option value="urgent">ASAP (Rush Job)</option>
                            <option value="1-month">Within 1 Month</option>
                            <option value="3-months">Within 3 Months</option>
                            <option value="6-months">Within 6 Months</option>
                            <option value="flexible">Flexible Timeline</option>
                          </select>
                          {errors.timeline && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                              {errors.timeline}
                            </motion.p>
                          )}
                        </div>

                        {/* Budget - Full width on mobile */}
                        <div className="lg:col-span-2">
                          <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                            Budget Range *
                          </label>
                          <select
                            value={formData.budget}
                            onChange={(e) => handleInputChange('budget', e.target.value)}
                            className={`w-full px-4 py-4 sm:py-3 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm ${
                              errors.budget
                                ? 'border-error-500 focus:border-error-600'
                                : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                            } focus:ring-2 focus:ring-primary-500/20 focus:outline-none appearance-none`}
                          >
                            <option value="">Select budget range</option>
                            <option value="under-600k">Under R600,000</option>
                            <option value="600k-1.5m">R600,000 - R1,500,000</option>
                            <option value="1.5m-3m">R1,500,000 - R3,000,000</option>
                            <option value="3m-6m">R3,000,000 - R6,000,000</option>
                            <option value="over-6m">Over R6,000,000</option>
                          </select>
                          {errors.budget && (
                            <motion.p
                              initial={{ opacity: 0, y: -10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                            >
                              <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                              {errors.budget}
                            </motion.p>
                          )}
                        </div>
                      </div>

                      {/* COMMENTED OUT - Mobile-optimized estimated cost display */}
                      {/* {estimatedCost > 0 && (
                        <motion.div
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-6 p-4 bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-950/20 dark:to-secondary-950/20 rounded-xl border border-primary-200 dark:border-primary-800"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <Calculator className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2 flex-shrink-0" />
                              <span className="text-sm font-medium text-gray-600 dark:text-gray-400">Estimated Cost:</span>
                            </div>
                            <span className="text-lg sm:text-xl font-bold text-primary-600 dark:text-primary-400">
                              {formatZAR(estimatedCost)}
                            </span>
                          </div>
                          <p className="text-xs text-gray-500 dark:text-gray-500 mt-2 leading-relaxed">
                            *This is a preliminary estimate. Final pricing will be provided after consultation.
                          </p>
                        </motion.div>
                      )} */}
                    </div>
                  </motion.div>
                )}

                {/* Simplified Steps 3 & 4 for mobile - keeping existing structure but with mobile optimizations */}
                {(currentStep === 3 || currentStep === 4) && (
                  <motion.div
                    key={`step${currentStep}`}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.3 }}
                    className="h-full"
                  >
                    <div className="text-center py-8 sm:py-12">
                      <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-r from-accent-500 to-success-500 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                        <FileText className="w-8 h-8 sm:w-10 sm:h-10 text-white" />
                      </div>
                      <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2 sm:mb-4">
                        {currentStep === 3 ? 'Project Requirements' : 'Review & Submit'}
                      </h2>
                      <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mb-6 sm:mb-8 max-w-md mx-auto leading-relaxed">
                        {currentStep === 3
                          ? 'Tell us more about your specific requirements and project details.'
                          : 'Review your information and submit your quote request.'
                        }
                      </p>

                      {currentStep === 3 && (
                        <div className="text-left max-w-2xl mx-auto space-y-6">
                          {/* Project Description */}
                          <div>
                            <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                              Project Description *
                            </label>
                            <textarea
                              value={formData.description}
                              onChange={(e) => handleInputChange('description', e.target.value)}
                              rows={5}
                              className={`w-full px-4 py-4 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base resize-none focus:outline-none ${
                                errors.description
                                  ? 'border-error-500 focus:border-error-600'
                                  : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                              } focus:ring-2 focus:ring-primary-500/20`}
                              placeholder="Please describe your project requirements, goals, and any specific needs in detail..."
                            />
                            {errors.description && (
                              <motion.p
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                              >
                                <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                                {errors.description}
                              </motion.p>
                            )}
                          </div>

                          {/* Requirements Checklist - Mobile Optimized */}
                          <div>
                            <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-4">
                              Specific Requirements * (Select at least one)
                            </label>
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                              {[
                                'Water quality testing',
                                'System installation',
                                'Maintenance plan',
                                'Emergency support',
                                'Compliance certification',
                                'Training services',
                                'Documentation',
                                'Remote monitoring'
                              ].map((requirement) => (
                                <motion.label
                                  key={requirement}
                                  className={`flex items-center p-3 sm:p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                                    formData.requirements.includes(requirement)
                                      ? 'border-primary-500 bg-primary-50 dark:bg-primary-950/20'
                                      : 'border-gray-300 dark:border-gray-600 hover:border-primary-300 bg-white/70 dark:bg-gray-700/70'
                                  }`}
                                  whileHover={{ scale: 1.01 }}
                                  whileTap={{ scale: 0.99 }}
                                >
                                  <input
                                    type="checkbox"
                                    checked={formData.requirements.includes(requirement)}
                                    onChange={(e) => {
                                      const newRequirements = e.target.checked
                                        ? [...formData.requirements, requirement]
                                        : formData.requirements.filter(r => r !== requirement);
                                      handleInputChange('requirements', newRequirements);
                                    }}
                                    className="w-5 h-5 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 dark:focus:ring-primary-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 mr-3"
                                  />
                                  <span className="text-sm sm:text-base text-gray-700 dark:text-gray-300 font-medium">{requirement}</span>
                                  {formData.requirements.includes(requirement) && (
                                    <motion.div
                                      initial={{ scale: 0 }}
                                      animate={{ scale: 1 }}
                                      className="ml-auto"
                                    >
                                      <CheckCircle2 className="w-5 h-5 text-primary-600 dark:text-primary-400" />
                                    </motion.div>
                                  )}
                                </motion.label>
                              ))}
                            </div>
                            {errors.requirements && (
                              <motion.p
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="mt-3 text-sm text-error-600 dark:text-error-400 flex items-center bg-error-50 dark:bg-error-950/20 p-3 rounded-lg border border-error-200 dark:border-error-800"
                              >
                                <AlertCircle className="w-4 h-4 mr-2 flex-shrink-0" />
                                {errors.requirements}
                              </motion.p>
                            )}
                          </div>

                          {/* Selected requirements summary for mobile */}
                          {formData.requirements.length > 0 && (
                            <motion.div
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              className="p-4 bg-primary-50 dark:bg-primary-950/20 rounded-xl border border-primary-200 dark:border-primary-800"
                            >
                              <p className="text-sm font-medium text-primary-800 dark:text-primary-200 mb-2">
                                Selected Requirements ({formData.requirements.length}):
                              </p>
                              <div className="flex flex-wrap gap-2">
                                {formData.requirements.map((req, index) => (
                                  <span
                                    key={index}
                                    className="inline-flex items-center px-2 py-1 rounded-lg bg-primary-100 dark:bg-primary-900/30 text-xs font-medium text-primary-700 dark:text-primary-300"
                                  >
                                    {req}
                                  </span>
                                ))}
                              </div>
                            </motion.div>
                          )}
                        </div>
                      )}

                      {currentStep === 4 && (
                        <div className="text-left max-w-2xl mx-auto space-y-6">
                          {/* Final Details Form */}
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                            {/* Urgency Level */}
                            <div>
                              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                Urgency Level *
                              </label>
                              <select
                                value={formData.urgency}
                                onChange={(e) => handleInputChange('urgency', e.target.value)}
                                className={`w-full px-4 py-4 sm:py-3 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm ${
                                  errors.urgency
                                    ? 'border-error-500 focus:border-error-600'
                                    : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                                } focus:ring-2 focus:ring-primary-500/20 focus:outline-none appearance-none`}
                              >
                                <option value="">Select urgency</option>
                                <option value="low">Low - No rush</option>
                                <option value="medium">Medium - Standard timeline</option>
                                <option value="high">High - Priority project</option>
                                <option value="critical">Critical - Emergency</option>
                              </select>
                              {errors.urgency && (
                                <motion.p
                                  initial={{ opacity: 0, y: -10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                                >
                                  <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                                  {errors.urgency}
                                </motion.p>
                              )}
                            </div>

                            {/* Preferred Contact Method */}
                            <div>
                              <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                Preferred Contact Method *
                              </label>
                              <select
                                value={formData.preferredContact}
                                onChange={(e) => handleInputChange('preferredContact', e.target.value)}
                                className={`w-full px-4 py-4 sm:py-3 rounded-xl border-2 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base sm:text-sm ${
                                  errors.preferredContact
                                    ? 'border-error-500 focus:border-error-600'
                                    : 'border-gray-300 dark:border-gray-600 focus:border-primary-500'
                                } focus:ring-2 focus:ring-primary-500/20 focus:outline-none appearance-none`}
                              >
                                <option value="">Select contact method</option>
                                <option value="email">Email</option>
                                <option value="phone">Phone Call</option>
                                <option value="video">Video Call</option>
                                <option value="in-person">In-Person Meeting</option>
                              </select>
                              {errors.preferredContact && (
                                <motion.p
                                  initial={{ opacity: 0, y: -10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  className="mt-2 text-sm text-error-600 dark:text-error-400 flex items-center"
                                >
                                  <AlertCircle className="w-4 h-4 mr-1 flex-shrink-0" />
                                  {errors.preferredContact}
                                </motion.p>
                              )}
                            </div>
                          </div>

                          {/* Additional Notes */}
                          <div>
                            <label className="block text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">
                              Additional Notes (Optional)
                            </label>
                            <textarea
                              value={formData.additionalNotes}
                              onChange={(e) => handleInputChange('additionalNotes', e.target.value)}
                              rows={3}
                              className="w-full px-4 py-4 rounded-xl border-2 border-gray-300 dark:border-gray-600 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 transition-all duration-200 bg-white/70 dark:bg-gray-700/70 text-base resize-none focus:outline-none"
                              placeholder="Any additional information you'd like to share..."
                            />
                          </div>

                          {/* Quote Summary */}
                          <div className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-950/20 dark:to-secondary-950/20 rounded-xl p-4 sm:p-6 border border-primary-200 dark:border-primary-800">
                            <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                              <CheckCircle2 className="w-5 h-5 text-primary-600 dark:text-primary-400 mr-2" />
                              Quote Summary
                            </h3>
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                              <div className="flex justify-between sm:block">
                                <span className="text-gray-600 dark:text-gray-400">Service:</span>
                                <span className="font-medium text-gray-900 dark:text-white sm:block">
                                  {serviceOptions.find(s => s.id === formData.service)?.name || 'Not selected'}
                                </span>
                              </div>
                              <div className="flex justify-between sm:block">
                                <span className="text-gray-600 dark:text-gray-400">Timeline:</span>
                                <span className="font-medium text-gray-900 dark:text-white sm:block">
                                  {formData.timeline.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Not selected'}
                                </span>
                              </div>
                              <div className="flex justify-between sm:block">
                                <span className="text-gray-600 dark:text-gray-400">Budget:</span>
                                <span className="font-medium text-gray-900 dark:text-white sm:block">
                                  {formatBudgetRange(formData.budget)}
                                </span>
                              </div>
                              <div className="flex justify-between sm:block">
                                <span className="text-gray-600 dark:text-gray-400">Requirements:</span>
                                <span className="font-medium text-gray-900 dark:text-white sm:block">
                                  {formData.requirements.length} selected
                                </span>
                              </div>
                            </div>
                            {/* COMMENTED OUT - Estimated Cost section */}
                            {/* <div className="mt-4 pt-4 border-t border-primary-200 dark:border-primary-800">
                              <div className="flex justify-between items-center">
                                <span className="text-gray-600 dark:text-gray-400 font-medium">Estimated Cost:</span>
                                <span className="text-lg sm:text-xl font-bold text-primary-600 dark:text-primary-400">
                                  {formatZAR(estimatedCost)}
                                </span>
                              </div>
                              <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                                *Final pricing will be provided after consultation
                              </p>
                            </div> */}
                          </div>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Pricing Disclaimer */}
            {currentStep === totalSteps && (
              <div className="p-4 sm:p-6 lg:p-8 max-w-4xl mx-auto">
                <div className="mb-4 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="w-5 h-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                    <div className="text-sm">
                      <p className="font-semibold text-amber-800 dark:text-amber-300 mb-1">
                        Important Pricing Information
                      </p>
                      <p className="text-amber-700 dark:text-amber-400">
                        All pricing estimates shown are preliminary and may vary based on specific project requirements,
                        site conditions, and current market factors. The official detailed quote will be provided after
                        a comprehensive assessment and technical evaluation of your project.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Status Message */}
            {statusMessage && (
              <div className="p-4 sm:p-6 lg:p-8 max-w-4xl mx-auto">
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className={`p-4 sm:p-3 rounded-xl sm:rounded-lg border-2 ${
                    statusMessage.type === 'success'
                      ? 'bg-success-50 dark:bg-success-900/20 border-success-200 dark:border-success-800'
                      : statusMessage.type === 'error'
                      ? 'bg-error-50 dark:bg-error-900/20 border-error-200 dark:border-error-800'
                      : 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                  }`}
                >
                  <div className="flex items-start">
                    <div className="flex-shrink-0">
                      {statusMessage.type === 'success' ? (
                        <CheckCircle2 className={`h-5 w-5 text-success-600 dark:text-success-400`} />
                      ) : statusMessage.type === 'error' ? (
                        <AlertCircle className={`h-5 w-5 text-error-600 dark:text-error-400`} />
                      ) : (
                        <AlertCircle className={`h-5 w-5 text-blue-600 dark:text-blue-400`} />
                      )}
                    </div>
                    <div className="ml-3 flex-1">
                      <h4 className={`text-sm font-semibold ${
                        statusMessage.type === 'success'
                          ? 'text-success-800 dark:text-success-200'
                          : statusMessage.type === 'error'
                          ? 'text-error-800 dark:text-error-200'
                          : 'text-blue-800 dark:text-blue-200'
                      }`}>
                        {statusMessage.title}
                      </h4>
                      <p className={`mt-1 text-sm ${
                        statusMessage.type === 'success'
                          ? 'text-success-700 dark:text-success-300'
                          : statusMessage.type === 'error'
                          ? 'text-error-700 dark:text-error-300'
                          : 'text-blue-700 dark:text-blue-300'
                      }`}>
                        {statusMessage.message}
                      </p>

                      {statusMessage.showRetry && statusMessage.type === 'error' && (
                        <div className="mt-3 flex flex-col sm:flex-row gap-2">
                          <button
                            onClick={handleRetry}
                            disabled={isSubmitting}
                            className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-error-600 hover:bg-error-700 disabled:opacity-50 disabled:cursor-not-allowed text-white transition-colors"
                          >
                            <RefreshCw size={16} className="mr-2" />
                            {isSubmitting ? 'Retrying...' : `Retry${retryCount > 0 ? ` (${retryCount})` : ''}`}
                          </button>
                          <button
                            onClick={clearStatusMessage}
                            className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 transition-colors"
                          >
                            <X size={16} className="mr-2" />
                            Dismiss
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              </div>
            )}

            {/* Mobile-optimized navigation buttons */}
            <div className="p-4 sm:p-6 lg:p-8 bg-gray-50/80 dark:bg-gray-900/80 border-t border-gray-200 dark:border-gray-700">
              <div className="flex justify-between items-center max-w-4xl mx-auto">
                <motion.button
                  type="button"
                  onClick={prevStep}
                  disabled={currentStep === 1}
                  className={`flex items-center px-4 sm:px-6 py-3 sm:py-3 rounded-xl font-medium transition-all duration-200 ${
                    currentStep === 1
                      ? 'text-gray-400 cursor-not-allowed'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 active:scale-95'
                  }`}
                  whileHover={currentStep > 1 ? { x: -4 } : {}}
                  whileTap={currentStep > 1 ? { scale: 0.95 } : {}}
                >
                  <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                  <span className="text-sm sm:text-base">Previous</span>
                </motion.button>

                {currentStep < totalSteps ? (
                  <motion.button
                    type="button"
                    onClick={nextStep}
                    className="flex items-center px-6 sm:px-8 py-3 sm:py-3 bg-gradient-to-r from-primary-500 to-secondary-500 text-white font-semibold rounded-xl hover:from-primary-600 hover:to-secondary-600 transition-all duration-300 shadow-lg hover:shadow-xl text-sm sm:text-base"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <span>Next Step</span>
                    <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 ml-2" />
                  </motion.button>
                ) : (
                  <motion.button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex items-center px-6 sm:px-8 py-3 sm:py-3 bg-gradient-to-r from-success-500 to-accent-500 text-white font-semibold rounded-xl hover:from-success-600 hover:to-accent-600 transition-all duration-300 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed text-sm sm:text-base"
                    whileHover={!isSubmitting ? { scale: 1.02 } : {}}
                    whileTap={!isSubmitting ? { scale: 0.98 } : {}}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                        <span>Submitting...</span>
                      </>
                    ) : (
                      <>
                        <span>Submit Request</span>
                        <CheckCircle2 className="w-4 h-4 sm:w-5 sm:h-5 ml-2" />
                      </>
                    )}
                  </motion.button>
                )}
              </div>
            </div>
          </form>
        </motion.div>
        </div>
      </div>
    </div>
  );
};
export default QuoteRequest;