// SEO Testing Utilities
// This file contains utilities to test SEO implementation

export const validateSEOTags = () => {
  const tests = [];
  
  // Test title tag
  const title = document.querySelector('title');
  tests.push({
    test: 'Title tag exists',
    passed: !!title,
    value: title?.textContent || 'Not found'
  });
  
  // Test meta description
  const description = document.querySelector('meta[name="description"]');
  tests.push({
    test: 'Meta description exists',
    passed: !!description,
    value: description?.getAttribute('content') || 'Not found'
  });
  
  // Test canonical URL
  const canonical = document.querySelector('link[rel="canonical"]');
  tests.push({
    test: 'Canonical URL exists',
    passed: !!canonical,
    value: canonical?.getAttribute('href') || 'Not found'
  });
  
  // Test Open Graph title
  const ogTitle = document.querySelector('meta[property="og:title"]');
  tests.push({
    test: 'Open Graph title exists',
    passed: !!ogTitle,
    value: ogTitle?.getAttribute('content') || 'Not found'
  });
  
  // Test Open Graph description
  const ogDescription = document.querySelector('meta[property="og:description"]');
  tests.push({
    test: 'Open Graph description exists',
    passed: !!ogDescription,
    value: ogDescription?.getAttribute('content') || 'Not found'
  });
  
  // Test Twitter Card
  const twitterCard = document.querySelector('meta[name="twitter:card"]');
  tests.push({
    test: 'Twitter Card exists',
    passed: !!twitterCard,
    value: twitterCard?.getAttribute('content') || 'Not found'
  });
  
  // Test structured data
  const structuredData = document.querySelector('script[type="application/ld+json"]');
  tests.push({
    test: 'Structured data exists',
    passed: !!structuredData,
    value: structuredData ? 'Found JSON-LD script' : 'Not found'
  });
  
  return tests;
};

export const logSEOReport = () => {
  const tests = validateSEOTags();
  console.group('🔍 SEO Implementation Report');
  
  tests.forEach(test => {
    const status = test.passed ? '✅' : '❌';
    console.log(`${status} ${test.test}: ${test.value}`);
  });
  
  const passedTests = tests.filter(test => test.passed).length;
  const totalTests = tests.length;
  
  console.log(`\n📊 Summary: ${passedTests}/${totalTests} tests passed`);
  console.groupEnd();
};

// Auto-run in development mode
if (import.meta.env.DEV) {
  // Run after a short delay to ensure DOM is ready
  setTimeout(logSEOReport, 1000);
}
