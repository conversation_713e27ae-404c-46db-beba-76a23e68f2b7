import { useLocation } from 'react-router-dom';
import { seoConfig, defaultSEO, SEOConfig } from '../config/seo';

export const useSEO = (customSEO?: Partial<SEOConfig>): SEOConfig => {
  const location = useLocation();
  
  // Map routes to SEO config keys
  const getRouteKey = (pathname: string): string => {
    switch (pathname) {
      case '/':
        return 'home';
      case '/projects':
        return 'projects';
      case '/gallery':
        return 'gallery';
      case '/get-a-quote':
        return 'quote';
      case '/quote-success':
        return 'quoteSuccess';
      default:
        return 'home';
    }
  };
  
  const routeKey = getRouteKey(location.pathname);
  const routeSEO = seoConfig[routeKey] || defaultSEO;
  
  // Merge route SEO with custom SEO overrides
  return {
    ...routeSEO,
    ...customSEO
  };
};

export default useSEO;
