import React from 'react';
import SEO from './SEO';
import { useSEO } from '../../hooks/useSEO';
import { SEOConfig } from '../../config/seo';

interface PageWrapperProps {
  children: React.ReactNode;
  customSEO?: Partial<SEOConfig>;
}

const PageWrapper: React.FC<PageWrapperProps> = ({ children, customSEO }) => {
  const seoData = useSEO(customSEO);

  return (
    <>
      <SEO {...seoData} />
      {children}
    </>
  );
};

export default PageWrapper;
