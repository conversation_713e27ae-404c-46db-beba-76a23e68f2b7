import React, { useEffect, useRef, useMemo } from 'react';
import { gsap } from 'gsap';

interface Particle {
  x: number;
  y: number;
  size: number;
  speed: number;
  delay: number;
  color: string;
}

interface OrbitalRing {
  radius: number;
  speed: number;
  direction: number;
  opacity: number;
}

interface LoadingProps {
  isLoading: boolean;
}

const Loading: React.FC<LoadingProps> = ({ isLoading }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const logoRef = useRef<HTMLImageElement>(null);
  const orbitalRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<HTMLDivElement>(null);
  const liquidRef = useRef<SVGSVGElement>(null);
  const animationRef = useRef<gsap.core.Timeline[]>([]);

  const { particles, orbitalRings } = useMemo(() => {
    const particleCount = 20;
    const ringCount = 3;
    const particles: Particle[] = [];
    const orbitalRings: OrbitalRing[] = [];

    // Create floating particles
    for (let i = 0; i < particleCount; i++) {
      particles.push({
        x: Math.random() * 100,
        y: Math.random() * 100,
        size: 2 + Math.random() * 6,
        speed: 0.5 + Math.random() * 1.5,
        delay: Math.random() * 3,
        color: ['#60a5fa', '#3b82f6', '#2563eb', '#06b6d4', '#0891b2'][Math.floor(Math.random() * 5)]
      });
    }

    // Create orbital rings
    for (let i = 0; i < ringCount; i++) {
      orbitalRings.push({
        radius: 120 + (i * 40),
        speed: 8 + (i * 4),
        direction: i % 2 === 0 ? 1 : -1,
        opacity: 0.3 - (i * 0.08)
      });
    }

    return { particles, orbitalRings };
  }, []);

  useEffect(() => {
    if (!isLoading) return;

    // Clear any existing animations
    animationRef.current.forEach(anim => anim?.kill());
    animationRef.current = [];

    // Logo entrance animation
    if (logoRef.current) {
      const logoTl = gsap.timeline();

      // Initial state
      gsap.set(logoRef.current, {
        scale: 0,
        rotation: -180,
        opacity: 0,
        filter: 'blur(20px)'
      });

      // Entrance animation
      logoTl
        .to(logoRef.current, {
          scale: 1.2,
          rotation: 0,
          opacity: 1,
          filter: 'blur(0px)',
          duration: 1.2,
          ease: 'back.out(1.7)'
        })
        .to(logoRef.current, {
          scale: 1,
          duration: 0.3,
          ease: 'power2.out'
        })
        .to(logoRef.current, {
          filter: 'drop-shadow(0 0 30px rgba(59, 130, 246, 0.8))',
          duration: 0.5,
          ease: 'power2.inOut'
        });

      // Breathing animation
      const breatheTl = gsap.timeline({ repeat: -1, yoyo: true, delay: 1.5 });
      breatheTl.to(logoRef.current, {
        scale: 1.05,
        filter: 'drop-shadow(0 0 40px rgba(59, 130, 246, 1))',
        duration: 2,
        ease: 'sine.inOut'
      });

      animationRef.current.push(logoTl, breatheTl);
    }

    // Orbital rings animation
    if (orbitalRef.current) {
      const rings = orbitalRef.current.querySelectorAll('.orbital-ring');

      rings.forEach((ring, i) => {
        const ringData = orbitalRings[i];
        const ringTl = gsap.timeline({ repeat: -1 });

        gsap.set(ring, {
          rotation: Math.random() * 360
        });

        ringTl.to(ring, {
          rotation: `+=${360 * ringData.direction}`,
          duration: ringData.speed,
          ease: 'none'
        });

        animationRef.current.push(ringTl);
      });
    }

    // Particles animation
    if (particlesRef.current) {
      const particleElements = particlesRef.current.querySelectorAll('.particle');

      particleElements.forEach((particle, i) => {
        const particleData = particles[i];
        const particleTl = gsap.timeline({ repeat: -1, delay: particleData.delay });

        // Set initial random position
        gsap.set(particle, {
          x: `${particleData.x}vw`,
          y: `${particleData.y}vh`,
          scale: 0,
          opacity: 0
        });

        particleTl
          .to(particle, {
            scale: 1,
            opacity: 0.8,
            duration: 0.5,
            ease: 'power2.out'
          })
          .to(particle, {
            x: `${(particleData.x + (Math.random() - 0.5) * 40)}vw`,
            y: `${(particleData.y + (Math.random() - 0.5) * 40)}vh`,
            rotation: 360,
            duration: particleData.speed * 3,
            ease: 'sine.inOut'
          }, 0.5)
          .to(particle, {
            scale: 0,
            opacity: 0,
            duration: 0.5,
            ease: 'power2.in'
          }, '-=0.5');

        animationRef.current.push(particleTl);
      });
    }

    return () => {
      animationRef.current.forEach(anim => anim?.kill());
    };
  }, [isLoading, particles, orbitalRings]);

  if (!isLoading) return null;

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 z-50 flex items-center justify-center overflow-hidden"
      role="dialog"
      aria-label="Loading content"
      aria-live="polite"
    >
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 dark:from-black dark:via-blue-950 dark:to-indigo-950">
        {/* Liquid morphing background */}
        <svg
          ref={liquidRef}
          className="absolute inset-0 w-full h-full opacity-30"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
        >
          <defs>
            <radialGradient id="liquid-gradient" cx="50%" cy="50%" r="50%">
              <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.8" />
              <stop offset="50%" stopColor="#1d4ed8" stopOpacity="0.6" />
              <stop offset="100%" stopColor="#1e40af" stopOpacity="0.3" />
            </radialGradient>
            <filter id="liquid-blur">
              <feGaussianBlur in="SourceGraphic" stdDeviation="2" />
            </filter>
          </defs>
          <circle
            cx="20"
            cy="30"
            r="15"
            fill="url(#liquid-gradient)"
            filter="url(#liquid-blur)"
            className="animate-pulse"
          />
          <circle
            cx="80"
            cy="70"
            r="20"
            fill="url(#liquid-gradient)"
            filter="url(#liquid-blur)"
            className="animate-pulse"
            style={{ animationDelay: '1s' }}
          />
          <circle
            cx="60"
            cy="20"
            r="12"
            fill="url(#liquid-gradient)"
            filter="url(#liquid-blur)"
            className="animate-pulse"
            style={{ animationDelay: '2s' }}
          />
        </svg>

        {/* Floating particles */}
        <div ref={particlesRef} className="absolute inset-0">
          {particles.map((particle, i) => (
            <div
              key={i}
              className="particle absolute w-2 h-2 rounded-full opacity-60"
              style={{
                backgroundColor: particle.color,
                boxShadow: `0 0 10px ${particle.color}`,
              }}
            />
          ))}
        </div>
      </div>

      {/* Main Content Container */}
      <div className="relative z-10 flex flex-col items-center">
        {/* Orbital Rings */}
        <div ref={orbitalRef} className="absolute inset-0 flex items-center justify-center">
          {orbitalRings.map((ring, i) => (
            <div
              key={i}
              className="orbital-ring absolute border-2 rounded-full"
              style={{
                width: `${ring.radius}px`,
                height: `${ring.radius}px`,
                borderColor: `rgba(59, 130, 246, ${ring.opacity})`,
                borderStyle: 'dashed',
                borderSpacing: '10px',
              }}
            />
          ))}
        </div>

        {/* Logo Container */}
        <div className="relative z-20 mb-12">
          <img
            ref={logoRef}
            src="/pazogen_logo_FULL.png"
            alt="Pazogen Logo"
            className="h-32 w-auto object-contain"
          />
        </div>

        {/* Modern Loading Text */}
        <div className="text-center space-y-6">
          <h2 className="text-4xl font-bold bg-gradient-to-r from-blue-400 via-cyan-400 to-blue-600 bg-clip-text text-transparent">
            PAZOGEN
          </h2>

          <div className="relative">
            <p className="text-blue-200 text-lg font-medium tracking-wider uppercase">
              Initializing Water Solutions
            </p>
            <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-32 h-0.5 bg-gradient-to-r from-transparent via-cyan-400 to-transparent"></div>
          </div>

          {/* Futuristic Progress Indicator */}
          <div className="flex justify-center items-center space-x-3 mt-8">
            {[0, 1, 2, 3, 4].map((i) => (
              <div
                key={i}
                className="w-3 h-3 rounded-full bg-gradient-to-r from-blue-400 to-cyan-400"
                style={{
                  animation: `modernPulse 2s ease-in-out ${i * 0.2}s infinite`,
                  boxShadow: '0 0 15px rgba(59, 130, 246, 0.8)',
                }}
              />
            ))}
          </div>

          {/* Holographic Loading Bar */}
          <div className="w-64 h-1 bg-gray-800 rounded-full overflow-hidden mt-6">
            <div className="h-full bg-gradient-to-r from-blue-500 via-cyan-400 to-blue-500 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>

      <style>{`
        @keyframes modernPulse {
          0%, 100% {
            opacity: 0.3;
            transform: scale(0.8);
            box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
          }
          50% {
            opacity: 1;
            transform: scale(1.2);
            box-shadow: 0 0 25px rgba(59, 130, 246, 1);
          }
        }

        .orbital-ring {
          animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }

        /* Glassmorphism effect */
        .glass-effect {
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
      `}</style>
    </div>
  );
};

export default Loading;
